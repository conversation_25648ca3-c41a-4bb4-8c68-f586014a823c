import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/settings/settings_controller.dart';

class FontSizeHelper {
  static SettingsController? _settingsController;

  static SettingsController get _controller {
    _settingsController ??= Get.find<SettingsController>();
    return _settingsController!;
  }

  // Lấy cỡ chữ hiện tại
  static double get currentFontSize => _controller.currentFontSize.value ?? 15;

  // Các cỡ chữ khác nhau
  static double get titleFontSize => _controller.titleFontSize;
  static double get subtitleFontSize => _controller.subtitleFontSize;
  static double get captionFontSize => _controller.captionFontSize;

  // Tạo TextStyle với cỡ chữ động
  static TextStyle getTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    TextDecoration? decoration,
    FontStyle? fontStyle,
  }) {
    return GoogleFonts.inter(
      fontSize: fontSize ?? currentFontSize,
      fontWeight: fontWeight,
      color: color,
      height: height,
      decoration: decoration,
      fontStyle: fontStyle,
    );
  }

  // TextStyle cho tiêu đề
  static TextStyle getTitleStyle({
    FontWeight? fontWeight,
    Color? color,
    double? height,
    FontStyle? fontStyle,
  }) {
    return GoogleFonts.inter(
      fontSize: titleFontSize,
      fontWeight: fontWeight ?? FontWeight.w600,
      color: color,
      height: height,
      fontStyle: fontStyle,
    );
  }

  // TextStyle cho subtitle
  static TextStyle getSubtitleStyle({
    FontWeight? fontWeight,
    Color? color,
    double? height,
    FontStyle? fontStyle,
  }) {
    return GoogleFonts.inter(
      fontSize: subtitleFontSize,
      fontWeight: fontWeight ?? FontWeight.w500,
      color: color,
      height: height,
      fontStyle: fontStyle,
    );
  }

  // TextStyle cho caption
  static TextStyle getCaptionStyle({
    FontWeight? fontWeight,
    Color? color,
    double? height,
  }) {
    return GoogleFonts.inter(
      fontSize: captionFontSize,
      fontWeight: fontWeight ?? FontWeight.w400,
      color: color,
      height: height,
    );
  }

  // TextStyle cho button
  static TextStyle getButtonStyle({
    FontWeight? fontWeight,
    Color? color,
  }) {
    return GoogleFonts.inter(
      fontSize: currentFontSize,
      fontWeight: fontWeight ?? FontWeight.w500,
      color: color,
    );
  }
}

// Extension cho TextStyle để dễ sử dụng
extension TextStyleExtension on TextStyle {
  TextStyle get withDynamicSize {
    return copyWith(fontSize: FontSizeHelper.currentFontSize);
  }

  TextStyle get withTitleSize {
    return copyWith(fontSize: FontSizeHelper.titleFontSize);
  }

  TextStyle get withSubtitleSize {
    return copyWith(fontSize: FontSizeHelper.subtitleFontSize);
  }

  TextStyle get withCaptionSize {
    return copyWith(fontSize: FontSizeHelper.captionFontSize);
  }
}
