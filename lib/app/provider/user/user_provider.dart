import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/app/model/user/danh_ba_model.dart';
import 'package:vnpt_ioffice_camau/app/model/user/thong_tin_ca_nhan.dart';
import 'package:vnpt_ioffice_camau/app/provider/api/user_api.dart';
import 'package:vnpt_ioffice_camau/app/provider/api_provider.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';

class UserProvider {
  final dio = ApiRoot().dio;
  final GetStorage _store = GetStorage();

  Future<ThongTinCaNhan> getThongTinCaNhan() async {
    try {
      var refreshToken = _store.read(GetStorageKey.refreshToken);

      final response = await dio
          .get('${UserApi.userDsCanBobyId}?refresh_token=$refreshToken');
      return ThongTinCaNhan.fromJson(response.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<dynamic> dangkyOTP(String sdt) async {
    try {
      var maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      var data = FormData.fromMap(
          {"dang_ky_otp": 0, "di_dong_can_bo": sdt, "ma_can_bo_kc": maCtcbKc});
      final response = await dio.post(UserApi.usDangkyOTP, data: data);
      return response;
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<dynamic> kiemTraTrungMatKhau(String passWord) async {
    try {
      var maCanBo = _store.read(GetStorageKey.maCanBo);
      var data = FormData.fromMap({'ma_can_bo': maCanBo, 'password': passWord});
      final response = await dio.post(UserApi.usKiemTraTrungMk, data: data);
      return response.data;
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<dynamic> capNhatMatKhau(
      String matKhauCu, String mauKhauMoi, String nhapLaiMatKhauMoi) async {
    try {
      var maCanBo = _store.read(GetStorageKey.maCanBo);
      var data = FormData.fromMap({
        'macanbo': maCanBo,
        'matkhaucu': matKhauCu,
        'matkhaumoi': mauKhauMoi,
        'nhaplaimatkhaumoi': nhapLaiMatKhauMoi
      });
      final response = await dio.post(UserApi.usCapNhatMatKhau, data: data);
      return response.data;
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<DanhBaModel> getDanhDaTheoDonVi() async {
    try {
      var maCanBo = _store.read(GetStorageKey.maCanBo);
      var maDonVi = _store.read(GetStorageKey.maDonVi);
      final response = await dio.get(
        '${UserApi.danhSachDanhBanDv}?ma_don_vi=$maDonVi&ma_can_bo=$maCanBo&checkeds=undefined',
      );
      return DanhBaModel.fromJson(response.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<dynamic> capNhatThongTinSmartCaTichHop(
      String uid, String password) async {
    try {
      var maCanBo = _store.read(GetStorageKey.maCtcbKc);
      var data = FormData.fromMap(
          {'ma_ctcb': maCanBo, 'uid': uid, 'password': password, 'totp': ''});
      final response =
          await dio.post(UserApi.capNhatThongTinSmartCaTichHop, data: data);
      return response.data;
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }
}
