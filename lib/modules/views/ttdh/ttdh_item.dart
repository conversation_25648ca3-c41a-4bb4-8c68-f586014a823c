import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';

class ItemThongTinDieuHanh extends StatelessWidget {
  final String tieuDe;
  final String NoiDung;
  final String ngayBanHanh;
  final VoidCallback onClickItem;
  const ItemThongTinDieuHanh(
      {super.key,
      required this.tieuDe,
      required this.NoiDung,
      required this.ngayBanHanh,
      required this.onClickItem});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        decoration: const BoxDecoration(
            color: Colors.white,
            border: Border(bottom: BorderSide(width: 0.3))),
        child: Row(
          children: [
            Expanded(
              flex: 1,
              child: Padding(
                  padding: const EdgeInsets.all(10),
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(tieuDe,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14.0,
                            )),
                        const Padding(
                            padding: EdgeInsets.symmetric(vertical: 2.0)),
                        Text(
                          "Nội dung: ${MethodUntils.removeNoiDungTTDH(NoiDung)}",
                          style: FontSizeHelper.getTextStyle(),
                        ),
                        const Padding(
                            padding: EdgeInsets.symmetric(vertical: 2.0)),
                      ])),
            ),
            Flexible(
              flex: 1,
              fit: FlexFit.tight,
              child: Container(
                  child: Center(
                child: Text(ngayBanHanh, style: FontSizeHelper.getTextStyle()),
              )),
            ),
          ],
        ),
      ),
      onTap: onClickItem,
    );
  }
}
