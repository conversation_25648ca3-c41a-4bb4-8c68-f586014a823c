import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lottie/lottie.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/utils/empty_list.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/mauchuky/mauchuky_controller.dart';

class MauChuKy extends GetView<MauChuKyController> {
  MauChuKy({super.key});
  var store = GetStorage();
  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Column(
      children: [
        // Header Card for Adding New Signature
        Card(
          margin: const EdgeInsets.all(16),
          elevation: 8,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColor.blueAccentColor.withOpacity(0.8),
                  AppColor.blueAccentColor,
                ],
              ),
            ),
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.draw_outlined,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Obx(() => Text(
                            'Thêm mẫu chữ ký mới',
                            style: FontSizeHelper.getTitleStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          )),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // Signature Name Input
                TextFormField(
                  controller: controller.tenChuKyEditingController,
                  style: FontSizeHelper.getTextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    labelText: 'Tên chữ ký',
                    labelStyle:
                        FontSizeHelper.getTextStyle(color: Colors.white70),
                    prefixIcon:
                        const Icon(Icons.edit_outlined, color: Colors.white70),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.white30),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.white30),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide:
                          const BorderSide(color: Colors.white, width: 2),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                // Selected File Display
                Obx(() => Visibility(
                      visible: controller.selectedChoseFileName.value != "",
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.white30),
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Icon(
                                Icons.image_outlined,
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Obx(() => Text(
                                    controller.selectedChoseFileName.value ??
                                        '',
                                    style: FontSizeHelper.getTextStyle(
                                        color: Colors.white),
                                    overflow: TextOverflow.ellipsis,
                                  )),
                            ),
                            IconButton(
                              onPressed: () {
                                controller.xoaFileAnhCks();
                              },
                              icon: const Icon(
                                Icons.close_rounded,
                                color: Colors.white70,
                                size: 20,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )),
                const SizedBox(height: 16),
                // File Selection Button
                InkWell(
                  onTap: () => _showFileSelectionDialog(context),
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.white30),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.cloud_upload_outlined,
                          color: Colors.white70,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Obx(() => Text(
                                "Chọn ảnh chữ ký",
                                style: FontSizeHelper.getTextStyle(
                                    color: Colors.white70),
                              )),
                        ),
                        const Icon(
                          Icons.arrow_forward_ios,
                          color: Colors.white70,
                          size: 16,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Add Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: AppColor.blueAccentColor,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    onPressed: () {
                      controller.onThemMoiChuKy();
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.add_circle_outline, size: 20),
                        const SizedBox(width: 8),
                        Obx(() => Text(
                              "Thêm mới",
                              style: FontSizeHelper.getButtonStyle(
                                color: AppColor.blueAccentColor,
                                fontWeight: FontWeight.w600,
                              ),
                            )),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 20),

        // Section Title
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColor.blueAccentColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.collections_outlined,
                  color: AppColor.blueAccentColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Obx(() => Text(
                      'Danh sách mẫu chữ ký',
                      style: FontSizeHelper.getTitleStyle(
                        color: AppColor.blueAccentColor,
                        fontWeight: FontWeight.w600,
                      ),
                    )),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: controller.obx(
              (dsMauChuky) => Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: ListView.builder(
                      itemCount: dsMauChuky!.length,
                      itemBuilder: (context, index) {
                        final signature = dsMauChuky[index]!;
                        final isActive = signature.trangThai!.toInt() == 1;

                        return Card(
                          margin: const EdgeInsets.only(bottom: 12),
                          elevation: 4,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              border: isActive
                                  ? Border.all(color: Colors.green, width: 2)
                                  : null,
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Row(
                                children: [
                                  // Signature Image
                                  Container(
                                    width: 80,
                                    height: 80,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      border:
                                          Border.all(color: Colors.grey[300]!),
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(12),
                                      child: Image.network(
                                        MethodUntils.getUrlFile(
                                          signature.linkCks ?? "",
                                          store.read(GetStorageKey.domainFile),
                                        ),
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) {
                                          return Container(
                                            color: Colors.grey[100],
                                            child: const Icon(
                                              Icons
                                                  .image_not_supported_outlined,
                                              color: Colors.grey,
                                              size: 32,
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 16),

                                  // Signature Info
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Obx(() => Text(
                                                    signature.tenKySo ??
                                                        "Không có tên",
                                                    style: FontSizeHelper
                                                        .getTextStyle(
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      color: Colors.black87,
                                                    ),
                                                  )),
                                            ),
                                            if (isActive)
                                              Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                  horizontal: 8,
                                                  vertical: 4,
                                                ),
                                                decoration: BoxDecoration(
                                                  color: Colors.green
                                                      .withOpacity(0.1),
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  border: Border.all(
                                                      color: Colors.green),
                                                ),
                                                child: Row(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: [
                                                    const Icon(
                                                      Icons.check_circle,
                                                      color: Colors.green,
                                                      size: 14,
                                                    ),
                                                    const SizedBox(width: 4),
                                                    Obx(() => Text(
                                                          'Đang sử dụng',
                                                          style: FontSizeHelper
                                                              .getCaptionStyle(
                                                            color: Colors.green,
                                                            fontWeight:
                                                                FontWeight.w500,
                                                          ),
                                                        )),
                                                  ],
                                                ),
                                              ),
                                          ],
                                        ),
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            // Delete Button
                                            Container(
                                              decoration: BoxDecoration(
                                                color:
                                                    Colors.red.withOpacity(0.1),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              child: IconButton(
                                                onPressed: () {
                                                  _showDeleteConfirmDialog(
                                                    context,
                                                    signature.idKySo!.toInt(),
                                                    signature.tenKySo ??
                                                        "chữ ký này",
                                                  );
                                                },
                                                icon: const Icon(
                                                  Icons.delete_outline,
                                                  color: Colors.red,
                                                  size: 20,
                                                ),
                                                tooltip: 'Xóa chữ ký',
                                              ),
                                            ),
                                            const SizedBox(width: 8),

                                            // Set Active Button
                                            if (!isActive)
                                              Container(
                                                decoration: BoxDecoration(
                                                  color: AppColor
                                                      .blueAccentColor
                                                      .withOpacity(0.1),
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                ),
                                                child: IconButton(
                                                  onPressed: () {
                                                    controller.capNhatMauCks(
                                                      signature.idKySo!.toInt(),
                                                    );
                                                  },
                                                  icon: Icon(
                                                    Icons.check_circle_outline,
                                                    color: AppColor
                                                        .blueAccentColor,
                                                    size: 20,
                                                  ),
                                                  tooltip: 'Đặt làm mặc định',
                                                ),
                                              ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
              onEmpty: Center(
                  child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Lottie.asset("lottie/emptyBox.json", height: 200, width: 200),
                  const Text("Không có dữ liệu!")
                ],
              )),
              onLoading: SpinKitCircle(
                color: AppColor.blueAccentColor,
              )),
        ),
      ],
    ));
  }

  void _showFileSelectionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColor.blueAccentColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.cloud_upload_outlined,
                  color: AppColor.blueAccentColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Obx(() => Text(
                      'Chọn ảnh chữ ký',
                      style: FontSizeHelper.getTitleStyle(
                        color: AppColor.blueAccentColor,
                        fontWeight: FontWeight.w600,
                      ),
                    )),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: () {
                  controller.pickFile();
                },
                child: Container(
                  padding: const EdgeInsets.all(32),
                  decoration: BoxDecoration(
                    color: AppColor.blueAccentColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppColor.blueAccentColor.withOpacity(0.3),
                      style: BorderStyle.solid,
                      width: 2,
                    ),
                  ),
                  child: Column(
                    children: [
                      SvgPicture.asset(
                        'svg/upload.svg',
                        height: 60,
                        width: 60,
                      ),
                      const SizedBox(height: 12),
                      Obx(() => Text(
                            'Nhấn để chọn ảnh',
                            style: FontSizeHelper.getTextStyle(
                              color: AppColor.blueAccentColor,
                              fontWeight: FontWeight.w500,
                            ),
                          )),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              if (controller.selectedFileCks.value != null)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.green.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.check_circle,
                        color: Colors.green,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Obx(() => Text(
                              controller.selectedFileName.value ??
                                  'Đã chọn file',
                              style: FontSizeHelper.getTextStyle(
                                color: Colors.green[700],
                                fontWeight: FontWeight.w500,
                              ),
                              overflow: TextOverflow.ellipsis,
                            )),
                      ),
                    ],
                  ),
                ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                controller.selectedFileCks.value = File("");
                controller.selectedFileName.value = '';
                Navigator.of(context).pop();
              },
              child: Obx(() => Text(
                    'Hủy',
                    style: FontSizeHelper.getTextStyle(color: Colors.grey),
                  )),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColor.blueAccentColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              onPressed: () {
                controller.xacNhanFileCks();
                Navigator.of(context).pop();
              },
              child: Obx(() => Text(
                    'Xác nhận',
                    style: FontSizeHelper.getButtonStyle(color: Colors.white),
                  )),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmDialog(
      BuildContext context, int signatureId, String signatureName) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.warning_outlined,
                  color: Colors.red,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Obx(() => Text(
                      'Xác nhận xóa',
                      style: FontSizeHelper.getTitleStyle(
                        color: Colors.red,
                        fontWeight: FontWeight.w600,
                      ),
                    )),
              ),
            ],
          ),
          content: Obx(() => Text(
                'Bạn có chắc chắn muốn xóa "$signatureName"?\nHành động này không thể hoàn tác.',
                style: FontSizeHelper.getTextStyle(),
              )),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Obx(() => Text(
                    'Hủy',
                    style: FontSizeHelper.getTextStyle(color: Colors.grey),
                  )),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              onPressed: () {
                controller.xoaMauChuky(signatureId);
                Navigator.of(context).pop();
              },
              child: Obx(() => Text(
                    'Xóa',
                    style: FontSizeHelper.getButtonStyle(color: Colors.white),
                  )),
            ),
          ],
        );
      },
    );
  }
}
