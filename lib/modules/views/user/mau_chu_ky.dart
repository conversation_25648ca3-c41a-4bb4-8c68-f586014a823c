import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lottie/lottie.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/utils/empty_list.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/mauchuky/mauchuky_controller.dart';

class MauChuKy extends GetView<MauChuKyController> {
  MauChuKy({super.key});
  var store = GetStorage();
  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Column(
      children: [
        Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 10.0),
              child: TextFormField(
                controller: controller.tenChuKyEditingController,
                decoration: InputDecoration(
                    icon: Text(
                  "Tên chữ ký: ",
                  style: FontSizeHelper.getTextStyle(),
                )),
              ),
            ),
            Obx(() => Visibility(
                  visible: controller.selectedChoseFileName.value != "",
                  child: ListTile(
                    leading: const Icon(
                      Icons.image,
                      color: Colors.redAccent,
                    ),
                    title: Text(
                      controller.selectedChoseFileName.toString(),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      style: FontSizeHelper.getTextStyle(),
                    ),
                    trailing: IconButton(
                      onPressed: () {
                        controller.xoaFileAnhCks();
                      },
                      icon: const Icon(
                        Icons.close_outlined,
                        color: Colors.redAccent,
                      ),
                    ),
                  ),
                )),
            Padding(
              padding: const EdgeInsets.only(left: 10),
              child: InkWell(
                onTap: () async {
                  await Get.defaultDialog(
                      title: "Chọn ảnh chữ ký",
                      titleStyle: const FontSizeHelper.getTextStyle(),
                      content: Column(
                        children: [
                          GestureDetector(
                            onTap: () {
                              controller.pickFile();
                            },
                            child: SvgPicture.asset(
                              'svg/upload.svg',
                              height: 80,
                              width: 80,
                            ),
                          ),
                          if (controller.selectedFileCks.value != null)
                            Obx(() => Text(controller.selectedFileName.value!)),
                        ],
                      ),
                      actions: [
                        ElevatedButton(
                          style: ButtonStyle(
                              backgroundColor:
                                  MaterialStateProperty.all<Color>(Colors.red)),
                          onPressed: () {
                            controller.selectedFileCks.value = File("");
                            controller.selectedFileName.value = '';
                            Get.back();
                          },
                          child: const Text(
                            "Đóng",
                            style: TextStyle(color: Colors.white),
                          ),
                        ),
                        ElevatedButton(
                          style: ButtonStyle(
                              backgroundColor: MaterialStateProperty.all<Color>(
                                  Colors.blue)),
                          onPressed: () {
                            controller.xacNhanFileCks();
                            Get.back();
                          },
                          child: const Text(
                            "Xác nhận",
                            style: TextStyle(color: Colors.white),
                          ),
                        ),
                      ]);
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    const Icon(
                      Icons.attachment_outlined,
                      color: Colors.blue,
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Text(
                      "Chọn tập tin đính kèm",
                      style:
                          FontSizeHelper.getTextStyle( color: Colors.blue),
                    )
                  ],
                ),
              ),
            ),
            ElevatedButton(
                style: ButtonStyle(
                    backgroundColor:
                        MaterialStateProperty.all<Color>(Colors.blue)),
                onPressed: () {
                  controller.onThemMoiChuKy();
                },
                child: Obx(() => Obx(() => Text("Thêm mới", style: FontSizeHelper.getTextStyle( color: Colors.white)))))
          ],
        ),
        const SizedBox(
          height: 10,
        ),
        const Divider(
          height: 1,
        ),
        const SizedBox(
          height: 10,
        ),
        Expanded(
          child: controller.obx(
              (dsMauChuky) => ListView.builder(
                  itemCount: dsMauChuky!.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding:
                          const EdgeInsets.only(left: 5, right: 5, bottom: 5),
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Colors.grey,
                            width: 1,
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 10, right: 10, top: 3, bottom: 3),
                          child: Row(children: [
                            Expanded(
                              flex: 2,
                              child: Image.network(
                                  height: 100,
                                  width: 100,
                                  fit: BoxFit.cover,
                                  MethodUntils.getUrlFile(
                                      dsMauChuky[index]!.linkCks ?? "",
                                      store.read(GetStorageKey.domainFile))),
                            ),
                            Expanded(
                                flex: 3,
                                child: Center(
                                    child:
                                        Obx(() =>Text(dsMauChuky[index]?.tenKySo ?? "", style: FontSizeHelper.getTextStyle())),
                            Expanded(
                              flex: 1,
                              child: Column(
                                children: [
                                  IconButton(
                                    onPressed: () {
                                      controller.xoaMauChuky(
                                          dsMauChuky[index]!.idKySo!.toInt());
                                    },
                                    icon: const Icon(
                                      Icons.delete,
                                      color: Colors.red,
                                    ),
                                    iconSize: 20,
                                  ),
                                  dsMauChuky[index]!.trangThai!.toInt() == 1
                                      ? IconButton(
                                          onPressed: () => {},
                                          icon: const Icon(
                                            Icons.check,
                                            color: Colors.white,
                                          ),
                                          style: ButtonStyle(
                                            backgroundColor:
                                                MaterialStateProperty.all<
                                                    Color>(Colors.blue),
                                            shape: MaterialStateProperty.all(
                                              RoundedRectangleBorder(
                                                side: const BorderSide(
                                                    color: Colors
                                                        .blue), // Border color
                                                borderRadius:
                                                    BorderRadius.circular(
                                                        50), // Border radius
                                              ),
                                            ),
                                          ),
                                        )
                                      : IconButton(
                                          focusColor: Colors.blueAccent,
                                          onPressed: () {
                                            controller.capNhatMauCks(
                                                dsMauChuky[index]!
                                                    .idKySo!
                                                    .toInt());
                                          },
                                          icon: const Icon(
                                            Icons.check,
                                            color: Colors.blue,
                                          ),
                                          iconSize: 20,
                                        )
                                ],
                              ),
                            ),
                          ]),
                        ),
                      ),
                    );
                  }),
              onEmpty: Container(
                child: Center(
                    child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Lottie.asset("lottie/emptyBox.json",
                        height: 200, width: 200),
                    const Text("Không có dữ liệu!")
                  ],
                )),
              ),
              onLoading: SpinKitCircle(
                color: AppColor.blueAccentColor,
              )),
        ),
      ],
    ));
  }
}
