import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:load_switch/load_switch.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/user/user_controller.dart';

class ThongTinCaNhan extends GetView<UserController> {
  const ThongTinCaNhan({super.key});
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Profile Header Card
            Card(
              elevation: 8,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColor.blueAccentColor.withOpacity(0.8),
                      AppColor.blueAccentColor,
                    ],
                  ),
                ),
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    // Avatar with border
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.white,
                          width: 4,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: const CircleAvatar(
                        radius: 60,
                        backgroundImage: AssetImage("images/avatar_default.png"),
                        backgroundColor: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Welcome text
                    Obx(() => Text(
                      controller.thongTinCaNhan.value.hoVaTenCanBo ?? 'Người dùng',
                      style: FontSizeHelper.getTitleStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    )),
                    const SizedBox(height: 8),
                    Obx(() => Text(
                      controller.thongTinCaNhan.value.tenChucVu ?? '',
                      style: FontSizeHelper.getSubtitleStyle(
                        color: Colors.white.withOpacity(0.9),
                      ),
                      textAlign: TextAlign.center,
                    )),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            // Information Cards
            controller.obx(
              (ttcn) => Column(
                children: [
                  // Personal Information Card
                  Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.person_outline,
                                color: AppColor.blueAccentColor,
                                size: 24,
                              ),
                              const SizedBox(width: 8),
                              Obx(() => Text(
                                'Thông tin cá nhân',
                                style: FontSizeHelper.getTitleStyle(
                                  color: AppColor.blueAccentColor,
                                  fontWeight: FontWeight.w600,
                                ),
                              )),
                            ],
                          ),
                          const SizedBox(height: 16),
                          _buildInfoRow(Icons.badge_outlined, 'Họ tên', ttcn!.hoVaTenCanBo ?? ''),
                          _buildInfoRow(Icons.phone_outlined, 'Điện thoại', ttcn.diDongCanBo ?? ''),
                          _buildInfoRow(Icons.cake_outlined, 'Ngày sinh', ttcn.ngaySinhVn ?? ''),
                          _buildInfoRow(Icons.wc_outlined, 'Giới tính', ttcn.gioiTinh == 1 ? 'Nam' : 'Nữ'),
                          _buildInfoRow(Icons.work_outline, 'Chức vụ', ttcn.tenChucVu ?? ''),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              onLoading: Center(
                child: SpinKitCircle(
                  color: AppColor.blueAccentColor,
                  size: 50,
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Action Buttons Card
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.settings_outlined,
                          color: AppColor.blueAccentColor,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        Obx(() => Text(
                          'Cài đặt tài khoản',
                          style: FontSizeHelper.getTitleStyle(
                            color: AppColor.blueAccentColor,
                            fontWeight: FontWeight.w600,
                          ),
                        )),
                      ],
                    ),
                    const SizedBox(height: 20),
                    // Modern Action Buttons
                    _buildActionButton(
                      icon: Icons.lock_outline,
                      title: 'Đổi mật khẩu',
                      subtitle: 'Thay đổi mật khẩu đăng nhập',
                      color: AppColor.blueAccentColor,
                      onTap: () => controller.showModalDoiMatKhau(),
                    ),
                    const SizedBox(height: 12),
                    _buildActionButton(
                      icon: Icons.security_outlined,
                      title: 'Smart CA tích hợp',
                      subtitle: 'Cấu hình thông tin Smart CA',
                      color: Colors.green,
                      onTap: () => _showSmartCADialog(context),
                    ),
                    const SizedBox(height: 12),
                    _buildActionButton(
                      icon: Icons.logout_outlined,
                      title: 'Đăng xuất',
                      subtitle: 'Thoát khỏi tài khoản hiện tại',
                      color: Colors.red,
                      onTap: () => controller.dangXuat(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: Obx(() => Text(
              label,
              style: FontSizeHelper.getTextStyle(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            )),
          ),
          Expanded(
            flex: 3,
            child: Obx(() => Text(
              value,
              style: FontSizeHelper.getTextStyle(
                color: Colors.black87,
                fontWeight: FontWeight.w600,
              ),
            )),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Obx(() => Text(
                    title,
                    style: FontSizeHelper.getTextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  )),
                  const SizedBox(height: 4),
                  Obx(() => Text(
                    subtitle,
                    style: FontSizeHelper.getCaptionStyle(
                      color: Colors.grey[600],
                    ),
                  )),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  void _showSmartCADialog(BuildContext context) {
    final TextEditingController uidController = TextEditingController(
      text: controller.thongTinCaNhan.value.smartcaThUid ?? '',
    );
    final TextEditingController passwordController = TextEditingController(
      text: controller.thongTinCaNhan.value.smartcaThPassword ?? '',
    );
    final RxBool isPasswordVisible = false.obs;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Obx(() => Text(
                'Thông tin SmartCA tích hợp',
                style: FontSizeHelper.getTitleStyle(),
              )),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: uidController,
                  decoration: InputDecoration(
                    labelText: 'UID',
                    labelStyle: FontSizeHelper.getTextStyle(),
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.person),
                  ),
                  style: FontSizeHelper.getTextStyle(),
                ),
                const SizedBox(height: 16),
                Obx(() => TextField(
                      controller: passwordController,
                      obscureText: !isPasswordVisible.value,
                      decoration: InputDecoration(
                        labelText: 'Mật khẩu',
                        labelStyle: FontSizeHelper.getTextStyle(),
                        border: const OutlineInputBorder(),
                        prefixIcon: const Icon(Icons.lock),
                        suffixIcon: IconButton(
                          icon: Icon(
                            isPasswordVisible.value
                                ? Icons.visibility
                                : Icons.visibility_off,
                            color: Colors.grey,
                          ),
                          onPressed: () {
                            isPasswordVisible.value = !isPasswordVisible.value;
                          },
                        ),
                      ),
                      style: FontSizeHelper.getTextStyle(),
                    )),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Obx(() => Text(
                    'Hủy',
                    style: FontSizeHelper.getTextStyle(color: Colors.grey),
                  )),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColor.blueAccentColor,
              ),
              onPressed: () {
                // Handle save smart CA info
                _saveSmartCAInfo(uidController.text, passwordController.text);
                Navigator.of(context).pop();
              },
              child: Obx(() => Text(
                    'Lưu',
                    style: FontSizeHelper.getButtonStyle(color: Colors.white),
                  )),
            ),
          ],
        );
      },
    );
  }

  void _saveSmartCAInfo(String uid, String password) async {
    if (uid.isEmpty || password.isEmpty) {
      Get.snackbar(
        'Lỗi',
        'Vui lòng nhập đầy đủ thông tin',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );
      return;
    }

    try {
      // Show loading
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(),
        ),
        barrierDismissible: false,
      );

      // Call API through controller
      final response = await controller.userProvider
          .capNhatThongTinSmartCaTichHop(uid, password);

      // Close loading dialog
      Get.back();

      // Handle response
      if (response != null) {
        Map<String, dynamic> result = response;
        if (result['id'] == 1) {
          // Reload user information after successful save
          controller.getThongTinCaNhan();

          Get.snackbar(
            'Thành công',
            'Đã cập nhật thông tin Smart CA thành công',
            backgroundColor: Colors.green,
            colorText: Colors.white,
            snackPosition: SnackPosition.TOP,
          );
        } else {
          Get.snackbar(
            'Lỗi',
            result['message'] ?? 'Có lỗi xảy ra khi cập nhật thông tin',
            backgroundColor: Colors.red,
            colorText: Colors.white,
            snackPosition: SnackPosition.TOP,
          );
        }
      }
    } catch (error) {
      // Close loading dialog if still open
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'Lỗi',
        'Có lỗi xảy ra: ${error.toString()}',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );
    }
  }
}
