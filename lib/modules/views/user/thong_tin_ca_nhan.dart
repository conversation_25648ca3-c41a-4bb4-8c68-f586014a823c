import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:load_switch/load_switch.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/user/user_controller.dart';

class ThongTinCaNhan extends GetView<UserController> {
  const ThongTinCaNhan({super.key});
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        child: Container(
          width: Get.width,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            // mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Padding(
                padding: EdgeInsets.only(top: 30, bottom: 20),
                child: SizedB<PERSON>(
                  height: 150,
                  width: 150,
                  child: CircleAvatar(
                    radius: 30.0,
                    backgroundImage: AssetImage("images/avatar_default.png"),
                    backgroundColor: Colors.transparent,
                  ),
                ),
              ),
              controller.obx(
                  (ttcn) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          RichText(
                            text: TextSpan(
                              text: 'Họ tên: ',
                              style: const TextStyle(
                                  color: Colors.grey, fontSize: 20),
                              children: <TextSpan>[
                                TextSpan(
                                    text: ttcn!.hoVaTenCanBo,
                                    style:
                                        const TextStyle(color: Colors.black)),
                              ],
                            ),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          RichText(
                            text: TextSpan(
                              text: 'Điện thoại: ',
                              style: FontSizeHelper.getTextStyle(
                                  color: Colors.grey),
                              children: <TextSpan>[
                                TextSpan(
                                    text: ttcn.diDongCanBo,
                                    style:
                                        const TextStyle(color: Colors.black)),
                              ],
                            ),
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          RichText(
                            text: TextSpan(
                              text: 'Ngày sinh: ',
                              style: const TextStyle(
                                  color: Colors.grey, fontSize: 20),
                              children: <TextSpan>[
                                TextSpan(
                                    text: ttcn.ngaySinhVn,
                                    style:
                                        const TextStyle(color: Colors.black)),
                              ],
                            ),
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          RichText(
                            text: TextSpan(
                              text: 'Giới tính: ',
                              style: const TextStyle(
                                  color: Colors.grey, fontSize: 20),
                              children: <TextSpan>[
                                TextSpan(
                                    text: ttcn.gioiTinh == 1 ? 'Nam' : 'Nữ',
                                    style:
                                        const TextStyle(color: Colors.black)),
                              ],
                            ),
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          RichText(
                            text: TextSpan(
                              text: 'Chức vụ: ',
                              style: const TextStyle(
                                  color: Colors.grey, fontSize: 20),
                              children: <TextSpan>[
                                TextSpan(
                                    text: ttcn.tenChucVu,
                                    style:
                                        const TextStyle(color: Colors.black)),
                              ],
                            ),
                          ),
                        ],
                      ),
                  onLoading: SpinKitCircle(
                    color: AppColor.blueAccentColor,
                  )),
              const SizedBox(
                height: 10,
              ),
              // Row(
              //   mainAxisAlignment: MainAxisAlignment.center,
              //   children: [
              //     const Text(
              //       "Xác thực OTP:",
              //       style: TextStyle(color: Colors.grey, fontSize: 20),
              //     ),
              //     Obx(
              //       () => Padding(
              //         padding: const EdgeInsets.only(left: 10),
              //         child: LoadSwitch(
              //           height: 40,
              //           width: 85,
              //           value: controller.isDangKyOtp.value,
              //           future: controller.onDangKyOtp,
              //           style: SpinStyle.material,
              //           onChange: (v) {
              //             controller.isDangKyOtp.value = !v;
              //             print(
              //                 'Value changed to q ${controller.isDangKyOtp.value}');
              //             print('Value changed to $v');
              //           },
              //           onTap: (v) {
              //             print('Tapping while value is $v');
              //           },
              //         ),
              //       ),
              //     )
              //   ],
              // ),
              const SizedBox(
                height: 10,
              ),
              Column(
                children: [
                  Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                    ElevatedButton(
                        style: ElevatedButton.styleFrom(
                            backgroundColor: AppColor.blueAccentColor,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(30))),
                        onPressed: () {
                          controller.showModalDoiMatKhau();
                        },
                        child: Obx(() => Text(
                              "Đổi mật khẩu",
                              style: FontSizeHelper.getButtonStyle(
                                  color: Colors.white),
                            ))),
                    const SizedBox(
                      width: 10,
                    ),
                    ElevatedButton(
                        style: ElevatedButton.styleFrom(
                            backgroundColor: AppColor.blueAccentColor,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(30))),
                        onPressed: () {
                          controller.dangXuat();
                        },
                        child: Obx(() => Text(
                              "Đăng xuất",
                              style: FontSizeHelper.getButtonStyle(
                                  color: Colors.white),
                            )))
                  ]),
                  const SizedBox(height: 15),
                  ElevatedButton(
                      style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(30))),
                      onPressed: () {
                        _showSmartCADialog(context);
                      },
                      child: Obx(() => Text(
                            "Thông tin smart CA tích hợp",
                            style: FontSizeHelper.getButtonStyle(
                                color: Colors.white),
                          ))),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSmartCADialog(BuildContext context) {
    final TextEditingController uidController = TextEditingController();
    final TextEditingController passwordController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Obx(() => Text(
                'Thông tin Smart CA tích hợp',
                style: FontSizeHelper.getTitleStyle(),
              )),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: uidController,
                  decoration: InputDecoration(
                    labelText: 'UID',
                    labelStyle: FontSizeHelper.getTextStyle(),
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.person),
                  ),
                  style: FontSizeHelper.getTextStyle(),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: passwordController,
                  obscureText: true,
                  decoration: InputDecoration(
                    labelText: 'Mật khẩu',
                    labelStyle: FontSizeHelper.getTextStyle(),
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.lock),
                  ),
                  style: FontSizeHelper.getTextStyle(),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Obx(() => Text(
                    'Hủy',
                    style: FontSizeHelper.getTextStyle(color: Colors.grey),
                  )),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColor.blueAccentColor,
              ),
              onPressed: () {
                // Handle save smart CA info
                _saveSmartCAInfo(uidController.text, passwordController.text);
                Navigator.of(context).pop();
              },
              child: Obx(() => Text(
                    'Lưu',
                    style: FontSizeHelper.getButtonStyle(color: Colors.white),
                  )),
            ),
          ],
        );
      },
    );
  }

  void _saveSmartCAInfo(String uid, String password) {
    // TODO: Implement save smart CA info logic
    if (uid.isNotEmpty && password.isNotEmpty) {
      Get.snackbar(
        'Thành công',
        'Đã lưu thông tin Smart CA',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );
    } else {
      Get.snackbar(
        'Lỗi',
        'Vui lòng nhập đầy đủ thông tin',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );
    }
  }
}
