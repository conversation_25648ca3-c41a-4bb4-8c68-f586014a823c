import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';

import 'package:badges/badges.dart' as badges;
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/dsvt_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbde/item_listvt_vbde.dart';

class SoVanBanDenVanThu extends GetView<VanThuController> {
  final HomeController homeController = Get.find();
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          Container(
            child: Column(children: [
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: SizedBox(
                  width: 1020,
                  child: TabBar(
                      labelColor: AppColor.helpBlue,
                      controller: controller.tabController,
                      onTap: (value) {
                        controller.getDsVanBanByIndexTab(value);
                      },
                      tabs: [
                        Obx(
                          () => Tab(
                              icon: (homeController.nvSoVbde == 0)
                                  ? const Text("VB điện tử")
                                  : badges.Badge(
                                      badgeStyle: const badges.BadgeStyle(
                                        badgeColor: Colors.red,
                                      ),
                                      position: badges.BadgePosition.topEnd(
                                          top: -20, end: -12),
                                      badgeContent: Text(
                                        homeController.nvSoVbde.toString(),
                                        style: const TextStyle(
                                            color: Colors.white),
                                      ),
                                      child: const Text("VB điện tử"),
                                    )),
                        ),
                        const Tab(text: "VB lưu tạm"),
                        const Tab(text: "VB LĐ trả lại"),
                        const Tab(text: "VB chờ LĐ duyệt"),
                        const Tab(text: "VB chờ chuyển"),
                        const Tab(text: "VB đã chuyển"),
                        const Tab(text: "VB đã huỷ"),
                      ]),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(5.0),
                child: SizedBox(
                  height: 45,
                  child: TextFormField(
                      cursorColor: const Color.fromARGB(255, 242, 237, 237),
                      style: const TextStyle(color: AppColor.blackColor),
                      onFieldSubmitted: (newValue) {
                        controller.setKeyWordSearch(newValue);
                      },
                      decoration: InputDecoration(
                          errorStyle: TextStyle(color: AppColor.helpBlue),
                          border: OutlineInputBorder(
                            borderSide:
                                BorderSide(width: 1, color: AppColor.helpBlue),
                          ),
                          labelStyle: TextStyle(color: AppColor.helpBlue),
                          focusColor: AppColor.blackColor,
                          prefixIcon: Icon(
                            Icons.search_outlined,
                            color: AppColor.helpBlue,
                            size: 20,
                          ),
                          hintText: "Nhập nội dung tìm kiếm...",
                          hintStyle: FontSizeHelper.getTextStyle())),
                ),
              )
            ]),
          ),
          Expanded(
            child: TabBarView(
              controller: controller.tabController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                Container(
                    color: AppColor.whiteColor,
                    child: controller.obx(
                        (dsVanBanDienTu) => controller.indexTabGobal.value != 0
                            ? const Text("")
                            : RefreshIndicator(
                                onRefresh: () async {
                                  controller.getDsVanBanByIndexTab(0);
                                },
                                child: ListView.builder(
                                    controller:
                                        controller.scrollControllerVanDienTu,
                                    physics: const BouncingScrollPhysics(),
                                    itemCount: controller.dsDienTu.length,
                                    itemBuilder: ((context, index) =>
                                        ItemListVtVbde(
                                          trichYeu:
                                              dsVanBanDienTu![index].trichYeu ??
                                                  "",
                                          soKyHieu:
                                              dsVanBanDienTu![index].soKyHieu ??
                                                  "",
                                          isXem: dsVanBanDienTu![index].ngayXem,
                                          coQuanBanHanh: dsVanBanDienTu![index]
                                                  .tenCoQuanBanHanh ??
                                              "",
                                          ngayVanBanDen:
                                              dsVanBanDienTu![index].ngayDen ??
                                                  "",
                                          isDoKhan: dsVanBanDienTu![index]
                                              .maCapDoKhan,
                                          onClickItem: () {
                                            controller.onPressItem(
                                                dsVanBanDienTu![index]);
                                          },
                                        ))),
                              ),
                        onLoading: SpinKitCircle(
                          color: AppColor.blueAccentColor,
                        ),
                        onEmpty: Container(
                          child: Center(
                              child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Lottie.asset("lottie/emptyBox.json",
                                  height: 200, width: 200),
                              const Text("Không tìm thấy dữ liệu!")
                            ],
                          )),
                        ))),
                Container(
                  color: AppColor.whiteColor,
                  child: controller.obx(
                      (dsVanBanLuuTam) => controller.indexTabGobal.value != 1
                          ? const Text("")
                          : RefreshIndicator(
                              onRefresh: () async {
                                controller.getDsVanBanByIndexTab(1);
                              },
                              child: ListView.builder(
                                  controller: controller.scrollControllerLuuTam,
                                  physics: const BouncingScrollPhysics(),
                                  itemCount: controller.dsVbLuuTamVt.length,
                                  itemBuilder: ((context, index1) =>
                                      ItemListVtVbde(
                                        trichYeu:
                                            dsVanBanLuuTam![index1].trichYeu ??
                                                "",
                                        soKyHieu:
                                            dsVanBanLuuTam![index1].soKyHieu ??
                                                "",
                                        isXem: dsVanBanLuuTam![index1].ngayXem,
                                        coQuanBanHanh: dsVanBanLuuTam![index1]
                                                .tenCoQuanBanHanh ??
                                            "",
                                        ngayVanBanDen:
                                            dsVanBanLuuTam![index1].ngayDen ??
                                                "",
                                        isDoKhan:
                                            dsVanBanLuuTam![index1].maCapDoKhan,
                                        onClickItem: () {
                                          controller.onPressItem(
                                              dsVanBanLuuTam![index1]);
                                        },
                                      ))),
                            ),
                      onLoading: SpinKitCircle(
                        color: AppColor.blueAccentColor,
                      ),
                      onEmpty: Container(
                        child: Center(
                            child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Lottie.asset("lottie/emptyBox.json",
                                height: 200, width: 200),
                            const Text("Không tìm thấy dữ liệu!")
                          ],
                        )),
                      )),
                ),
                Container(
                    color: AppColor.whiteColor,
                    child: controller.obx(
                        (dsVanBanLdTraLai) => controller.indexTabGobal.value !=
                                2
                            ? const Text("")
                            : RefreshIndicator(
                                onRefresh: () async {
                                  controller.getDsVanBanByIndexTab(2);
                                },
                                child: ListView.builder(
                                    controller:
                                        controller.scrollControllerLdTraLai,
                                    physics: const BouncingScrollPhysics(),
                                    itemCount: controller.dsVbLuuTamVt.length,
                                    itemBuilder: ((context, index) =>
                                        ItemListVtVbde(
                                          trichYeu: dsVanBanLdTraLai![index]
                                                  .trichYeu ??
                                              "",
                                          soKyHieu: dsVanBanLdTraLai![index]
                                                  .soKyHieu ??
                                              "",
                                          isXem:
                                              dsVanBanLdTraLai![index].ngayXem,
                                          coQuanBanHanh:
                                              dsVanBanLdTraLai![index]
                                                      .tenCoQuanBanHanh ??
                                                  "",
                                          ngayVanBanDen:
                                              dsVanBanLdTraLai![index]
                                                      .ngayDen ??
                                                  "",
                                          isDoKhan: dsVanBanLdTraLai![index]
                                              .maCapDoKhan,
                                          onClickItem: () {
                                            controller.onDetaitsItem(
                                                dsVanBanLdTraLai![index]);
                                          },
                                        ))),
                              ),
                        onLoading: SpinKitCircle(
                          color: AppColor.blueAccentColor,
                        ),
                        onEmpty: Container(
                          child: Center(
                              child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Lottie.asset("lottie/emptyBox.json",
                                  height: 200, width: 200),
                              const Text("Không tìm thấy dữ liệu!")
                            ],
                          )),
                        ))),
                Container(
                    color: AppColor.whiteColor,
                    child: controller.obx(
                        (dsVanBanLdChoDuyet) => controller
                                    .indexTabGobal.value !=
                                3
                            ? const Text("")
                            : RefreshIndicator(
                                onRefresh: () async {
                                  controller.getDsVanBanByIndexTab(3);
                                },
                                child: ListView.builder(
                                    controller:
                                        controller.scrollControllerLdChoDuyet,
                                    physics: const BouncingScrollPhysics(),
                                    itemCount:
                                        controller.dsVbLdChoDuyetVt.length,
                                    itemBuilder: ((context, index) =>
                                        ItemListVtVbde(
                                          trichYeu: dsVanBanLdChoDuyet![index]
                                                  .trichYeu ??
                                              "",
                                          soKyHieu: dsVanBanLdChoDuyet![index]
                                                  .soKyHieu ??
                                              "",
                                          isXem: dsVanBanLdChoDuyet![index]
                                              .ngayXem,
                                          coQuanBanHanh:
                                              dsVanBanLdChoDuyet![index]
                                                      .tenCoQuanBanHanh ??
                                                  "",
                                          ngayVanBanDen:
                                              dsVanBanLdChoDuyet![index]
                                                      .ngayDen ??
                                                  "",
                                          isDoKhan: dsVanBanLdChoDuyet![index]
                                              .maCapDoKhan,
                                          onClickItem: () {
                                            controller.onDetaitsItem(
                                                dsVanBanLdChoDuyet![index]);
                                          },
                                        ))),
                              ),
                        onLoading: SpinKitCircle(
                          color: AppColor.blueAccentColor,
                        ),
                        onEmpty: Container(
                          child: Center(
                              child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Lottie.asset("lottie/emptyBox.json",
                                  height: 200, width: 200),
                              const Text("Không tìm thấy dữ liệu!")
                            ],
                          )),
                        ))),
                Container(
                    color: AppColor.whiteColor,
                    child: controller.obx(
                        (dsVanBanChoChuyen) => controller.indexTabGobal.value !=
                                4
                            ? const Text("")
                            : RefreshIndicator(
                                onRefresh: () async {
                                  controller.getDsVanBanByIndexTab(4);
                                },
                                child: ListView.builder(
                                    controller:
                                        controller.scrollControllerChoChuyen,
                                    physics: const BouncingScrollPhysics(),
                                    itemCount:
                                        controller.dsVbChoChuyenVt.length,
                                    itemBuilder: ((context, index) =>
                                        ItemListVtVbde(
                                          trichYeu: dsVanBanChoChuyen![index]
                                                  .trichYeu ??
                                              "",
                                          soKyHieu: dsVanBanChoChuyen![index]
                                                  .soKyHieu ??
                                              "",
                                          isXem:
                                              dsVanBanChoChuyen![index].ngayXem,
                                          coQuanBanHanh:
                                              dsVanBanChoChuyen![index]
                                                      .tenCoQuanBanHanh ??
                                                  "",
                                          ngayVanBanDen:
                                              dsVanBanChoChuyen![index]
                                                      .ngayDuyet ??
                                                  "",
                                          isDoKhan: dsVanBanChoChuyen![index]
                                              .maCapDoKhan,
                                          onClickItem: () {
                                            controller.onDetaitsItem(
                                                dsVanBanChoChuyen![index]);
                                          },
                                        ))),
                              ),
                        onLoading: SpinKitCircle(
                          color: AppColor.blueAccentColor,
                        ),
                        onEmpty: Container(
                          child: Center(
                              child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Lottie.asset("lottie/emptyBox.json",
                                  height: 200, width: 200),
                              const Text("Không tìm thấy dữ liệu!")
                            ],
                          )),
                        ))),
                Container(
                    color: AppColor.whiteColor,
                    child: controller.obx(
                        (dsVanBanDaChuyen) => controller.indexTabGobal.value !=
                                5
                            ? const Text("")
                            : RefreshIndicator(
                                onRefresh: () async {
                                  controller.getDsVanBanByIndexTab(5);
                                },
                                child: ListView.builder(
                                    controller:
                                        controller.scrollControllerDaChuyen,
                                    physics: const BouncingScrollPhysics(),
                                    itemCount: controller.dsVbDaChuyenVt.length,
                                    itemBuilder: ((context, index) =>
                                        ItemListVtVbde(
                                          trichYeu: dsVanBanDaChuyen![index]
                                                  .trichYeu ??
                                              "",
                                          soKyHieu: dsVanBanDaChuyen![index]
                                                  .soKyHieu ??
                                              "",
                                          isXem:
                                              dsVanBanDaChuyen![index].ngayXem,
                                          coQuanBanHanh:
                                              dsVanBanDaChuyen![index]
                                                      .tenCoQuanBanHanh ??
                                                  "",
                                          ngayVanBanDen:
                                              dsVanBanDaChuyen![index]
                                                      .ngayDuyet ??
                                                  "",
                                          isDoKhan: dsVanBanDaChuyen![index]
                                              .maCapDoKhan!,
                                          onClickItem: () {
                                            controller.onDetaitsItem(
                                                dsVanBanDaChuyen![index]);
                                          },
                                        ))),
                              ),
                        onLoading: SpinKitCircle(
                          color: AppColor.blueAccentColor,
                        ),
                        onEmpty: Container(
                          child: Center(
                              child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Lottie.asset("lottie/emptyBox.json",
                                  height: 200, width: 200),
                              const Text("Không tìm thấy dữ liệu!")
                            ],
                          )),
                        ))),
                Container(
                    color: AppColor.whiteColor,
                    child: controller.obx(
                        (dsVanBanDaHuy) => controller.indexTabGobal.value != 6
                            ? const Text("")
                            : RefreshIndicator(
                                onRefresh: () async {
                                  controller.getDsVanBanByIndexTab(6);
                                },
                                child: ListView.builder(
                                    controller:
                                        controller.scrollControllerDaHuy,
                                    physics: const BouncingScrollPhysics(),
                                    itemCount: controller.dsVbDaHuyVt.length,
                                    itemBuilder: ((context, index) =>
                                        ItemListVtVbde(
                                          trichYeu:
                                              dsVanBanDaHuy![index].trichYeu ??
                                                  "",
                                          soKyHieu:
                                              dsVanBanDaHuy![index].soKyHieu ??
                                                  "",
                                          isXem: dsVanBanDaHuy![index].ngayXem,
                                          coQuanBanHanh: dsVanBanDaHuy![index]
                                                  .tenCoQuanBanHanh ??
                                              "",
                                          ngayVanBanDen:
                                              dsVanBanDaHuy![index].ngayDuyet ??
                                                  "",
                                          isDoKhan:
                                              dsVanBanDaHuy![index].maCapDoKhan,
                                          onClickItem: () {
                                            controller.onDetaitsItem(
                                                dsVanBanDaHuy![index]);
                                          },
                                        ))),
                              ),
                        onLoading: SpinKitCircle(
                          color: AppColor.blueAccentColor,
                        ),
                        onEmpty: Container(
                          child: Center(
                              child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Lottie.asset("lottie/emptyBox.json",
                                  height: 200, width: 200),
                              const Text("Không tìm thấy dữ liệu!")
                            ],
                          )),
                        )))
              ],
            ),
          ),
        ],
      ),
    );
  }
}
