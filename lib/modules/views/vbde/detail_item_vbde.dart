import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/utils/full_screen_dialog_loader.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/global_widget/view_file_online.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/common/setup_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/chitiet_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/dsld_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class DetailItemVbde extends GetView<ChiTietVbdeController> {
  DetailItemVbde({super.key});
  final SetupController setupController = Get.find();
  @override
  Widget build(BuildContext context) {
    String isQuyen =
        Get.arguments['isQuyen'] == null ? "" : Get.arguments['isQuyen'];
    int indexTab =
        Get.arguments['indexTab'] == null ? 0 : Get.arguments['indexTab'];
    int maYeuCau = Get.arguments['maYeuCau'] == null
        ? 0
        : Get.arguments['maYeuCau'].toInt();
    return SafeArea(
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(10),
              child: Obx(() => Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      RichText(
                        text: TextSpan(
                          text: 'Trích yếu: ',
                          style: DefaultTextStyle.of(context)
                              .style, // Sử dụng phong cách mặc định
                          children: <TextSpan>[
                            TextSpan(
                              text: controller.vbdeDetail.value.trichYeu,
                              style: const TextStyle(
                                  fontWeight:
                                      FontWeight.bold), // Phong cách in đậm
                            )
                          ],
                        ),
                      ),
                      const Padding(padding: EdgeInsets.only(bottom: 10)),
                      Row(
                        children: [
                          RichText(
                            text: TextSpan(
                              text: 'Số Ký hiệu: ',
                              style: DefaultTextStyle.of(context)
                                  .style, // Sử dụng phong cách mặc định
                              children: <TextSpan>[
                                TextSpan(
                                  text: controller.vbdeDetail.value.soKyHieu,
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.red), // Phong cách in đậm
                                )
                              ],
                            ),
                          ),
                          const Padding(
                            padding: EdgeInsets.only(left: 10),
                          ),
                          RichText(
                            text: TextSpan(
                              text: 'Số đến: ',
                              style: DefaultTextStyle.of(context)
                                  .style, // Sử dụng phong cách mặc định
                              children: <TextSpan>[
                                TextSpan(
                                    text: controller.vbdeDetail.value.soDen ==
                                            null
                                        ? ""
                                        : controller.vbdeDetail.value.soDen!
                                            .toInt()
                                            .toString()

                                    // Phong cách in đậm
                                    )
                              ],
                            ),
                          ),
                        ],
                      ),
                      const Padding(
                        padding: EdgeInsets.only(bottom: 10),
                      ),
                      RichText(
                        text: TextSpan(
                          text: 'Ngày đến: ',
                          style: DefaultTextStyle.of(context).style,
                          children: <TextSpan>[
                            TextSpan(
                              text: controller.vbdeDetail.value.ngayDen ?? "",
                            )
                          ],
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(bottom: 10),
                      ),
                      RichText(
                        text: TextSpan(
                          text: 'Ngày ban hành: ',
                          style: DefaultTextStyle.of(context).style,
                          children: <TextSpan>[
                            TextSpan(
                                text: controller.vbdeDetail.value.ngayBanHanh ??
                                    "",
                                style:
                                    const TextStyle(color: AppColor.helpBlue))
                          ],
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(bottom: 10),
                      ),
                      RichText(
                        text: TextSpan(
                          text: 'Cơ quan ban hành: ',
                          style: DefaultTextStyle.of(context).style,
                          children: <TextSpan>[
                            TextSpan(
                                text: controller
                                        .vbdeDetail.value.tenCoQuanBanHanh ??
                                    "")
                          ],
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(bottom: 10),
                      ),
                      RichText(
                        text: TextSpan(
                          text: 'Nơi lưu bản chính: ',
                          style: DefaultTextStyle.of(context).style,
                          children: <TextSpan>[
                            TextSpan(
                                text: controller
                                        .vbdeDetail.value.noiLuuBanChinh ??
                                    "")
                          ],
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(bottom: 10),
                      ),
                      RichText(
                        text: TextSpan(
                          text: 'Lãnh đạo phê duyệt: ',
                          style: DefaultTextStyle.of(context).style,
                          children: <TextSpan>[
                            TextSpan(
                                text:
                                    controller.vbdeDetail.value.tenNguoiDuyet ??
                                        "",
                                style:
                                    const TextStyle(color: AppColor.helpBlue))
                          ],
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(bottom: 10),
                      ),
                      RichText(
                        text: TextSpan(
                          text: 'Loại công văn: ',
                          style: DefaultTextStyle.of(context).style,
                          children: <TextSpan>[
                            TextSpan(
                              text: controller.vbdeDetail.value.tenLoaiVanBan ??
                                  "",
                            )
                          ],
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(bottom: 10),
                      ),
                      RichText(
                        text: TextSpan(
                          text: 'Sổ văn bản: ',
                          style: DefaultTextStyle.of(context).style,
                          children: <TextSpan>[
                            TextSpan(
                              text:
                                  controller.vbdeDetail.value.tenSoVbDen ?? "",
                            )
                          ],
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(bottom: 10),
                      ),
                      RichText(
                        text: TextSpan(
                          text: 'Cấp độ khẩn: ',
                          style: DefaultTextStyle.of(context).style,
                          children: <TextSpan>[
                            TextSpan(
                              text: controller.vbdeDetail.value.tenCapDoKhan ??
                                  "",
                            )
                          ],
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(bottom: 10),
                      ),
                      RichText(
                        text: TextSpan(
                          text: 'Cấp độ mật: ',
                          style: TextStyle(color: Colors.grey[800]),
                          children: <TextSpan>[
                            TextSpan(
                                text: controller.vbdeDetail.value.tenCapDoMat ??
                                    "",
                                style: const TextStyle(
                                    color: AppColor.blackColor,
                                    fontWeight: FontWeight.w600))
                          ],
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(bottom: 10),
                      ),
                      RichText(
                        text: TextSpan(
                          text: 'Định danh: ',
                          style: TextStyle(color: Colors.grey[800]),
                          children: <TextSpan>[
                            TextSpan(
                                text: controller.vbdeDetail.value.maDinhDanhVb,
                                style: const TextStyle(
                                    color: AppColor.blackColor,
                                    fontWeight: FontWeight.w600))
                          ],
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(bottom: 10),
                      ),
                      Text(
                        "Tệp đính kèm: ",
                        style: TextStyle(color: Colors.grey[800]),
                      ),
                      (controller.vbdeDetail.value.fileVanBan != null)
                          ? ListView(
                              physics: const NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              children: List.generate(
                                  MethodUntils.getFileChiTietTTDH(controller
                                          .vbdeDetail.value.fileVanBan!
                                          .split(":"))
                                      .length, (index) {
                                return GestureDetector(
                                    onTap: () {
                                      ModalViewFileOnline.ViewFileOnline(
                                          tenFile: MethodUntils.getFileName(
                                              controller
                                                  .vbdeDetail.value.fileVanBan!
                                                  .split(":")[index]
                                                  .toString()),
                                          path: MethodUntils.getFileChiTietTTDH(
                                                  controller.vbdeDetail.value
                                                      .fileVanBan!
                                                      .split(":"))[index]
                                              .urlViewFile!,
                                          item: controller
                                              .vbdeDetail.value.fileVanBan!
                                              .split(":")[index]);
                                      // setupController.openFile(
                                      //     url: MethodUntils.getFileChiTietTTDH(
                                      //             controller.vbdeDetail.value
                                      //                 .fileVanBan!
                                      //                 .split(":"))[index]
                                      //         .urlViewFile!,
                                      //     fileName: MethodUntils.getFileName(
                                      //         controller
                                      //             .vbdeDetail.value.fileVanBan!
                                      //             .split(":")[index]
                                      //             .toString()));
                                    },
                                    child: ListTile(
                                      leading: Icon(
                                        MethodUntils.getFileChiTietTTDH(
                                                controller.vbdeDetail.value
                                                    .fileVanBan!
                                                    .split(":"))[index]
                                            .iconFile,
                                        color: AppColor.darkRedColor,
                                        size: 20,
                                      ),
                                      title: Text(
                                          MethodUntils.getFileName(controller
                                              .vbdeDetail.value.fileVanBan!
                                              .split(":")[index]
                                              .toString()),
                                          style: const TextStyle(
                                              color: AppColor.blackColor,
                                              fontSize: 12)),
                                    ));
                              }))
                          : const Text(""),
                      const Padding(
                        padding: EdgeInsets.only(bottom: 10),
                      ),
                      // Text(
                      //   "Văn bản liên quan:",
                      //   style: TextStyle(color: Colors.grey[800]),
                      // ),
                      // (controller.vbdeDetail.value.fileVanBanLienQuan != null)
                      //     ? ListView(
                      //         physics: const NeverScrollableScrollPhysics(),
                      //         shrinkWrap: true,
                      //         children: List.generate(
                      //             MethodUntils.getFileChiTietTTDH(controller
                      //                     .vbdeDetail.value.fileVanBanLienQuan!
                      //                     .split(":"))
                      //                 .length, (index) {
                      //           return GestureDetector(
                      //               onTap: () {
                      //                 setupController.openFile(
                      //                     url: MethodUntils.getFileChiTietTTDH(
                      //                             controller.vbdeDetail.value
                      //                                 .fileVanBanLienQuan!
                      //                                 .split(":"))[index]
                      //                         .urlViewFile!,
                      //                     fileName: MethodUntils.getFileName(
                      //                         controller.vbdeDetail.value
                      //                             .fileVanBanLienQuan!
                      //                             .split(":")[index]
                      //                             .toString()));
                      //               },
                      //               child: ListTile(
                      //                 leading: Icon(
                      //                   MethodUntils.getFileChiTietTTDH(
                      //                           controller.vbdeDetail.value
                      //                               .fileVanBanLienQuan!
                      //                               .split(":"))[index]
                      //                       .iconFile,
                      //                   color: MethodUntils.getFileChiTietTTDH(
                      //                           controller.vbdeDetail.value
                      //                               .fileVanBanLienQuan!
                      //                               .split(":"))[index]
                      //                       .colorIcon,
                      //                   size: 20,
                      //                 ),
                      //                 title: Text(
                      //                     MethodUntils.getFormatFileName(
                      //                         MethodUntils.getFileName(
                      //                             controller.vbdeDetail.value
                      //                                 .fileVanBanLienQuan!
                      //                                 .split(":")[index]
                      //                                 .toString())),
                      //                     style: const TextStyle(
                      //                         color: AppColor.blackColor,
                      //                         fontSize: 12)),
                      //               ));
                      //         }))
                      //     : const Text(""),
                      Visibility(
                        visible: controller.dsVanBanLienQuan.value!.isNotEmpty,
                        child: const Text(
                          "Văn bản liên quan:",
                          style: TextStyle(
                              color: AppColor.greyColor, fontSize: 15.0),
                        ),
                      ),
                      Container(
                        child: Obx(
                          () => ListView(
                              physics: const NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              children: List.generate(
                                  controller.dsVanBanLienQuan.value!.isEmpty
                                      ? 0
                                      : MethodUntils.getFileChiTietTTDH(
                                              controller.dsVanBanLienQuan.value!
                                                  .split(":"))
                                          .length, (index) {
                                return ListTile(
                                  leading: Icon(
                                    MethodUntils.getFileChiTietTTDH(controller
                                            .dsVanBanLienQuan.value!
                                            .split(":"))[index]
                                        .iconFile,
                                    color: MethodUntils.getFileChiTietTTDH(
                                            controller.dsVanBanLienQuan.value!
                                                .split(":"))[index]
                                        .colorIcon,
                                  ),
                                  title: GestureDetector(
                                    onTap: () {
                                      ModalViewFileOnline.ViewFileOnline(
                                          tenFile:
                                              MethodUntils.getFileChiTietTTDH(
                                                      controller
                                                          .dsVanBanLienQuan
                                                          .value!
                                                          .split(":"))[index]
                                                  .fileName!,
                                          item: controller
                                              .dsVanBanLienQuan.value!
                                              .split(":")[index],
                                          path: MethodUntils.getFileChiTietTTDH(
                                                  controller
                                                      .dsVanBanLienQuan.value!
                                                      .split(":"))[index]
                                              .urlViewFile!);
                                      // setupController.openFile(
                                      //     url:
                                      //         MethodUntils.getFileChiTietTTDH(
                                      //                 controller.item.value!
                                      //                     .data!.fileVanBan!
                                      //                     .split(":"))[index]
                                      //             .urlViewFile!,
                                      //     fileName:
                                      //         MethodUntils.getFileChiTietTTDH(
                                      //                 controller.item.value!
                                      //                     .data!.fileVanBan!
                                      //                     .split(":"))[index]
                                      //             .fileName!,
                                      //     indexFile: index);
                                    },
                                    child: Text(
                                      MethodUntils.getFileName(controller
                                          .dsVanBanLienQuan.value!
                                          .split(":")[index]
                                          .toString()),
                                      style: const TextStyle(
                                          color: AppColor.blackColor,
                                          fontSize: 13),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                );
                              })),
                        ),
                      ),
                      const Divider(
                        height: 2,
                        color: AppColor.greyColor,
                      ),
                      Visibility(
                          visible: ((isQuyen == 'isLanhDao') &&
                              (indexTab == 0 || indexTab == 1)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              ElevatedButton(
                                onPressed: () {
                                  controller.onSwitchPageDuyet();
                                  // Button onPressed callback
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue,
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 8, horizontal: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                ),
                                child: Text(
                                  'Duyệt',
                                  style: FontSizeHelper.getTextStyle(
                                      color: Colors.white),
                                ),
                              ),
                              const Padding(padding: EdgeInsets.only(left: 10)),
                              ElevatedButton(
                                onPressed: () {
                                  controller.onSwitchPageXuLy();
                                  // Button onPressed callback
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue,
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 8, horizontal: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                ),
                                child: Text(
                                  'Hoàn tất',
                                  style: FontSizeHelper.getTextStyle(
                                      color: Colors.white),
                                ),
                              ),
                              const Padding(padding: EdgeInsets.only(left: 10)),
                              ElevatedButton(
                                  onPressed: () {
                                    // Button onPressed callback
                                    controller.onSwitchPageChuyenLDKhac();
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 8, horizontal: 16),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5),
                                    ),
                                  ),
                                  child: Text(
                                    'Chuyển LĐK',
                                    style: FontSizeHelper.getTextStyle(
                                        color: Colors.white),
                                  )),
                            ],
                          )),
                      Visibility(
                          visible: (isQuyen == 'isLanhDao' && indexTab == 2),
                          child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                ElevatedButton(
                                  onPressed: () {
                                    controller.onSwitchPageDuyet();
                                    // Button onPressed callback
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 8, horizontal: 16),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5),
                                    ),
                                  ),
                                  child: Text(
                                    'Chuyển tiếp',
                                    style: FontSizeHelper.getTextStyle(
                                        color: Colors.white),
                                  ),
                                )
                              ])),
                      Visibility(
                          visible: (isQuyen == 'isChuyenVien' &&
                              (indexTab == 0 || indexTab == 1)),
                          child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                ElevatedButton(
                                  onPressed: () {
                                    controller.cvHoanThanhVbd();
                                    // Button onPressed callback
                                  },
                                  style: ElevatedButton.styleFrom(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 8, horizontal: 16),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                      backgroundColor: Colors.green),
                                  child: Text(
                                    'Hoàn thành ',
                                    style: FontSizeHelper.getTextStyle(
                                        color: Colors.white),
                                  ),
                                ),
                                const SizedBox(
                                  width: 10,
                                ),
                                Visibility(
                                  visible: maYeuCau != 1,
                                  child: ElevatedButton(
                                    onPressed: () {
                                      controller.onSwitchPageChuyenXLCV();
                                      // Button onPressed callback
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 8, horizontal: 16),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                    ),
                                    child: Text(
                                      'Chuyển ',
                                      style: FontSizeHelper.getTextStyle(
                                          color: Colors.white),
                                    ),
                                  ),
                                ),
                                const SizedBox(
                                  width: 10,
                                ),
                                Visibility(
                                  visible: maYeuCau != 1,
                                  child: ElevatedButton(
                                    onPressed: () {
                                      controller.cvDangXulyVbd();
                                      // Button onPressed callback
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 8, horizontal: 16),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                    ),
                                    child: Text(
                                      'Đang xử lý',
                                      style: FontSizeHelper.getTextStyle(
                                          color: Colors.white),
                                    ),
                                  ),
                                ),
                                Visibility(
                                  visible: maYeuCau == 1,
                                  child: ElevatedButton(
                                    onPressed: () {
                                      controller.onSwitchPageCvNhapYkien();
                                      // Button onPressed callback
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 8, horizontal: 16),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                    ),
                                    child: Text(
                                      'Nhập ý kiến',
                                      style: FontSizeHelper.getTextStyle(
                                          color: Colors.white),
                                    ),
                                  ),
                                )
                              ])),
                      Visibility(
                          visible: (isQuyen == 'isChuyenVien' && indexTab == 2),
                          child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                ElevatedButton(
                                  onPressed: () {
                                    controller.onSwitchPageChuyenXLCV();
                                    // Button onPressed callback
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 8, horizontal: 16),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5),
                                    ),
                                  ),
                                  child: Text(
                                    'Chuyển tiếp',
                                    style: FontSizeHelper.getTextStyle(
                                        color: Colors.white),
                                  ),
                                ),
                                const SizedBox(
                                  width: 10,
                                )
                              ])),
                      Visibility(
                          visible: (indexTab == 4),
                          child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                ElevatedButton(
                                  onPressed: () {
                                    controller.huyTheoDoiVbde();
                                    // Button onPressed callback
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 8, horizontal: 16),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5),
                                    ),
                                  ),
                                  child: Text(
                                    'Huỷ theo dỗi',
                                    style: FontSizeHelper.getTextStyle(
                                        color: Colors.white),
                                  ),
                                ),
                                const SizedBox(
                                  width: 10,
                                )
                              ]))
                    ],
                  )),
            ),
            Padding(
              padding: const EdgeInsets.all(10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const Text("Tổng hợp ý kiến xử lý",
                      style: TextStyle(
                          color: AppColor.blackColor,
                          fontWeight: FontWeight.w600)),
                  const Padding(padding: EdgeInsets.only(bottom: 10)),
                  const Divider(
                    height: 2,
                    color: AppColor.greyColor,
                  ),
                  Obx(() => Column(
                      children: controller.dsButphe
                          .map((butPhe) => Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const SizedBox(
                                      height: 5,
                                    ),
                                    Text(
                                      "${butPhe.hoVaTenCanBo} - ${butPhe.tenDonVi} (${butPhe.ngayNhan})",
                                      style: const TextStyle(
                                          color: AppColor.helpBlue),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.all(5),
                                      child: Text(
                                          "Chuyển: ${MethodUntils.getChuoiNguoiNhan(butPhe.tenNguoiNhan!)}"),
                                    ),
                                    (butPhe.noiDungChuyen == null)
                                        ? const Text("")
                                        : Padding(
                                            padding: const EdgeInsets.all(5),
                                            child: Text(
                                                "Nội dung: ${butPhe.noiDungChuyen}"),
                                          ),
                                    const Divider(
                                      color: Colors.grey,
                                    )
                                  ]))
                          .toList())),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
