import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/dscv_vbdi_controller.dart';
import 'package:badges/badges.dart' as badges;
import 'package:vnpt_ioffice_camau/modules/views/vbdi/item_listld_vbdi.dart';

class DsXuLyCvVbdi extends GetView<DsCvVbdiController> {
  DsXuLyCvVbdi();
  HomeController homeController = Get.find();
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          Container(
            child: Column(children: [
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: SizedBox(
                  width: 550,
                  child: TabBar(
                      padding: const EdgeInsets.fromLTRB(0, 5, 0, 5),
                      labelColor: AppColor.helpBlue,
                      controller: controller.tabController,
                      onTap: (value) {
                        controller.onChangeTab(value);
                      },
                      tabs: [
                        Obx(() => Tab(
                            icon: (homeController.nvVbdiXuLy == 0)
                                ? const Text("Chưa xử lý")
                                : badges.Badge(
                                    badgeStyle: const badges.BadgeStyle(
                                      badgeColor: Colors.red,
                                    ),
                                    position: badges.BadgePosition.topEnd(
                                        top: -20, end: -12),
                                    badgeContent: Text(
                                      homeController.nvVbdiXuLy.toString(),
                                      style:
                                          const TextStyle(color: Colors.white),
                                    ),
                                    child: const Text("Chưa xử lý"),
                                  ))),
                        const Tab(text: "Đã xử lý"),
                        const Tab(text: "Chờ phát hành"),
                        const Tab(text: "Đã phát hành"),
                      ]),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(5.0),
                child: SizedBox(
                  height: 45,
                  child: TextFormField(
                      cursorColor: const Color.fromARGB(255, 242, 237, 237),
                      style: const TextStyle(color: AppColor.blackColor),
                      onFieldSubmitted: (value) {
                        controller.setKeySearch(value);
                      },
                      decoration: InputDecoration(
                          errorStyle: const TextStyle(color: AppColor.helpBlue),
                          border: const OutlineInputBorder(
                            borderSide:
                                BorderSide(width: 1, color: AppColor.helpBlue),
                          ),
                          labelStyle: const TextStyle(color: AppColor.helpBlue),
                          focusColor: AppColor.blackColor,
                          prefixIcon: const Icon(
                            Icons.search_outlined,
                            color: AppColor.helpBlue,
                            size: 20,
                          ),
                          hintText: "Nhập nội dung tìm kiếm...",
                          hintStyle: FontSizeHelper.getTextStyle())),
                ),
              ),
            ]),
          ),
          Expanded(
            child: TabBarView(
              controller: controller.tabController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                Container(
                    color: AppColor.whiteColor,
                    child: controller.obx(
                        (dsCvChuaXuLy) => controller.indexTabGobal.value != 0
                            ? const Text("")
                            : RefreshIndicator(
                                onRefresh: () => controller.loadDsChuaXuLy(),
                                child: ListView.builder(
                                    controller:
                                        controller.scrollerControllerChuaXuLy,
                                    physics:
                                        const AlwaysScrollableScrollPhysics(),
                                    itemCount: controller.listChuaXulyCv.length,
                                    itemBuilder: ((context, index) =>
                                        ItemListLdVbdi(
                                          trichYeu:
                                              dsCvChuaXuLy![index].trichYeu ??
                                                  "",
                                          isXem: dsCvChuaXuLy![index]
                                                  .xem!
                                                  .toInt() ??
                                              0,
                                          doMat:
                                              dsCvChuaXuLy![index].tenCapDoMat!,
                                          doKhan: dsCvChuaXuLy![index]
                                              .tenCapDoKhan!,
                                          coQuanBanHanh: dsCvChuaXuLy![index]
                                                  .tenCoQuanBanHanh ??
                                              "",
                                          ngayNhan:
                                              dsCvChuaXuLy![index].ngayNhan ??
                                                  "",
                                          onClickItem: () {
                                            controller.onPressDetaisl(
                                                dsCvChuaXuLy![index]
                                                    .maVanBanDiKc!
                                                    .toInt(),
                                                dsCvChuaXuLy![index]
                                                    .maXuLyDi!
                                                    .toInt(),
                                                controller.indexTabGobal.value);
                                          },
                                        ))),
                              ),
                        onLoading: SpinKitCircle(
                          color: AppColor.blueAccentColor,
                        ),
                        onEmpty: Container(
                          child: Center(
                              child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Lottie.asset("lottie/emptyBox.json",
                                  height: 200, width: 200),
                              const Text("Không tìm thấy dữ liệu!")
                            ],
                          )),
                        ))),
                Container(
                    color: AppColor.whiteColor,
                    child: controller.obx(
                        (dsCvDaXuLy) => controller.indexTabGobal.value != 1
                            ? const Text("")
                            : RefreshIndicator(
                                onRefresh: () => controller.loadDsDaXuLy(),
                                child: ListView.builder(
                                    controller:
                                        controller.scrollerControllerDaXuLy,
                                    physics:
                                        const AlwaysScrollableScrollPhysics(),
                                    itemCount: controller.listDaXuLyCv.length,
                                    itemBuilder: ((context, index) =>
                                        ItemListLdVbdi(
                                          trichYeu:
                                              dsCvDaXuLy![index].trichYeu ?? "",
                                          isXem:
                                              dsCvDaXuLy![index].xem!.toInt() ??
                                                  0,
                                          doMat:
                                              dsCvDaXuLy![index].tenCapDoMat!,
                                          doKhan:
                                              dsCvDaXuLy![index].tenCapDoKhan!,
                                          coQuanBanHanh: dsCvDaXuLy![index]
                                                  .tenCoQuanBanHanh ??
                                              "",
                                          ngayNhan:
                                              dsCvDaXuLy![index].ngayNhan ?? "",
                                          onClickItem: () {
                                            controller.onPressDetaisl(
                                                dsCvDaXuLy![index]
                                                    .maVanBanDiKc!
                                                    .toInt(),
                                                dsCvDaXuLy![index]
                                                    .maXuLyDi!
                                                    .toInt(),
                                                controller.indexTabGobal.value);
                                          },
                                        ))),
                              ),
                        onLoading: SpinKitCircle(
                          color: AppColor.blueAccentColor,
                        ),
                        onEmpty: Container(
                          child: Center(
                              child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Lottie.asset("lottie/emptyBox.json",
                                  height: 200, width: 200),
                              const Text("Không tìm thấy dữ liệu!")
                            ],
                          )),
                        ))),
                Container(
                    color: AppColor.whiteColor,
                    child: controller.obx(
                        (dsChoPhatHanh) => controller.indexTabGobal.value != 2
                            ? const Text("")
                            : RefreshIndicator(
                                onRefresh: () => controller.loadDsChoPhatHanh(),
                                child: ListView.builder(
                                    controller: controller
                                        .scrollerControllerChoPhatHanh,
                                    physics:
                                        const AlwaysScrollableScrollPhysics(),
                                    itemCount:
                                        controller.listChoPhatHanh.length,
                                    itemBuilder: ((context, index) =>
                                        ItemListLdVbdi(
                                          trichYeu:
                                              dsChoPhatHanh![index].trichYeu ??
                                                  "",
                                          isXem: dsChoPhatHanh![index]
                                                  .xem!
                                                  .toInt() ??
                                              0,
                                          doMat: dsChoPhatHanh![index]
                                              .tenCapDoMat!,
                                          doKhan: dsChoPhatHanh![index]
                                              .tenCapDoKhan!,
                                          coQuanBanHanh: dsChoPhatHanh![index]
                                                  .tenCoQuanBanHanh ??
                                              "",
                                          ngayNhan:
                                              dsChoPhatHanh![index].ngayNhan ??
                                                  "",
                                          onClickItem: () {
                                            var maXulyDi = dsChoPhatHanh![index]
                                                        .maXuLyDi !=
                                                    null
                                                ? dsChoPhatHanh![index]
                                                    .maXuLyDi!
                                                    .toInt()
                                                : 0;
                                            controller.onPressDetaisl(
                                                dsChoPhatHanh![index]
                                                    .maVanBanDiKc!
                                                    .toInt(),
                                                maXulyDi,
                                                controller.indexTabGobal.value);
                                          },
                                        ))),
                              ),
                        onLoading: SpinKitCircle(
                          color: AppColor.blueAccentColor,
                        ),
                        onEmpty: Container(
                          child: Center(
                              child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Lottie.asset("lottie/emptyBox.json",
                                  height: 200, width: 200),
                              const Text("Không tìm thấy dữ liệu!")
                            ],
                          )),
                        ))),
                Container(
                    color: AppColor.whiteColor,
                    child: controller.obx(
                        (dsDaPhatHanh) => controller.indexTabGobal.value != 3
                            ? const Text("")
                            : RefreshIndicator(
                                onRefresh: () => controller.loadDsDaPhatHanh(),
                                child: ListView.builder(
                                    controller:
                                        controller.scrollerControllerDaPhatHanh,
                                    physics:
                                        const AlwaysScrollableScrollPhysics(),
                                    itemCount: controller.listDaPhatHanh.length,
                                    itemBuilder: ((context, index) =>
                                        ItemListLdVbdi(
                                          trichYeu:
                                              dsDaPhatHanh![index].trichYeu ??
                                                  "",
                                          isXem: dsDaPhatHanh![index]
                                                  .xem!
                                                  .toInt() ??
                                              0,
                                          doMat:
                                              dsDaPhatHanh![index].tenCapDoMat!,
                                          doKhan: dsDaPhatHanh![index]
                                              .tenCapDoKhan!,
                                          coQuanBanHanh: dsDaPhatHanh![index]
                                                  .tenCoQuanBanHanh ??
                                              "",
                                          ngayNhan:
                                              dsDaPhatHanh![index].ngayNhan ??
                                                  "",
                                          onClickItem: () {
                                            controller.onPressDetaisl(
                                                dsDaPhatHanh![index]
                                                    .maVanBanDiKc!
                                                    .toInt(),
                                                dsDaPhatHanh![index]
                                                    .maXuLyDi!
                                                    .toInt(),
                                                controller.indexTabGobal.value);
                                          },
                                        ))),
                              ),
                        onLoading: SpinKitCircle(
                          color: AppColor.blueAccentColor,
                        ),
                        onEmpty: Container(
                          child: Center(
                              child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Lottie.asset("lottie/emptyBox.json",
                                  height: 200, width: 200),
                              const Text("Không tìm thấy dữ liệu!")
                            ],
                          )),
                        ))),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
