import 'dart:ffi';

import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/tracuu/tracuu_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/views/tracuu/tracuu_item.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class SearchVanBanDen extends GetView<TraCuuVbdeController> {
  SearchVanBanDen({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Padding(
        padding: const EdgeInsets.only(top: 10),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 5, left: 10, right: 5),
              child: Row(
                children: [
                  Expanded(
                    flex: 4,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 2, right: 2),
                      child: TextFormField(
                        controller: controller.inputTrichYeuVbde,
                        onSaved: (value) {
                          controller.inputTrichYeuVbde.text = value!;
                        },
                        validator: (value) {
                          if (value!.isEmpty) {
                            return '';
                          }
                          return null;
                        },
                        decoration: const InputDecoration(
                          labelText: 'Tra cứu theo trích yếu/skh...',
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: DropdownButtonFormField2<int>(
                      isExpanded: true,
                      decoration: InputDecoration(
                        // Add Horizontal padding using menuItemStyleData.padding so it matches
                        // the menu padding when button's width is not specified.
                        contentPadding:
                            const EdgeInsets.symmetric(vertical: 16),
                        border: OutlineInputBorder(
                          borderSide: BorderSide(width: 1, color: Colors.grey),
                        ),
                        // Add more decoration..
                      ),
                      hint: Text(
                        'Năm',
                        style: FontSizeHelper.getTextStyle(),
                      ),
                      value: controller.selectYear.value,
                      items: controller.years
                          .map((item) => DropdownMenuItem<int>(
                                value: item,
                                child: Text(
                                  item.toString(),
                                  style: FontSizeHelper.getTextStyle(),
                                ),
                              ))
                          .toList(),
                      validator: (value) {
                        if (value == null) {
                          return 'Please select gender.';
                        }
                        return null;
                      },
                      onChanged: (value) {
                        controller.selectYear.value = value!;
                        //Do something when selected item is changed.
                      },
                      onSaved: (value) {
                        controller.selectYear.value = value!;
                      },
                      buttonStyleData: const ButtonStyleData(
                        padding: EdgeInsets.only(right: 8),
                      ),
                      iconStyleData: const IconStyleData(
                        icon: Icon(
                          Icons.arrow_drop_down,
                          color: Colors.black45,
                        ),
                        iconSize: 24,
                      ),
                      dropdownStyleData: DropdownStyleData(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                        ),
                      ),
                      menuItemStyleData: const MenuItemStyleData(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 2),
                      child: Container(
                        height: 55,
                        width: 50,
                        decoration: BoxDecoration(
                          color: AppColor
                              .blueAccentColor, // Set the background color here
                        ),
                        child: IconButton(
                          color: Colors.white,
                          onPressed: () {
                            controller.loadDanhSachVanBanDen();
                          },
                          icon: const Icon(Icons.search),
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
            const Divider(
              height: 20.0,
              color: Colors.blueAccent,
            ),
            Expanded(
              flex: 1,
              child: controller.obx(
                  (dsTraCuuVbde) => ListView.builder(
                      controller: controller.scrollerTraCuuVanBanDen,
                      itemCount: controller.dsVanBanDen.length,
                      itemBuilder: ((context, index) => ItemTraCuuVanBan(
                            trichYeu: controller.dsVanBanDen[index].trichYeu!,
                            sokyhieu: controller.dsVanBanDen[index].soKyHieu!,
                            loaiVanBan:
                                controller.dsVanBanDen[index].tenLoaiVanBan!,
                            ngayBanHanh: controller
                                        .dsVanBanDen[index].ngayBanHanh ==
                                    null
                                ? ""
                                : DateFormat('dd/MM/yyyy').format(
                                    controller.dsVanBanDen[index].ngayBanHanh!),
                            nguoiky:
                                controller.dsVanBanDen[index].nguoiKy ?? "",
                            onClickItem: () {
                              controller.onSwitchPage(
                                  controller.dsVanBanDen[index].maVanBanDenKc!
                                      .toInt(),
                                  0);
                            },
                          ))),
                  onLoading: SpinKitCircle(
                    color: AppColor.blueAccentColor,
                  ),
                  onEmpty: Container(
                    child: Center(
                        child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Lottie.asset("lottie/emptyBox.json",
                            height: 200, width: 200),
                        const Text("Không có dữ liệu!")
                      ],
                    )),
                  )),
            )
          ],
        ),
      ),
    );
  }
}
